<div>
   <div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center flex-sm-row flex-column">
                <div class="page-title-wrap">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    @lang('accounting.proposals.view_proposal')
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                               <a href="">@lang('accounting.dashboard')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a href="{{ route('finance.proposal') }}">@lang('accounting.proposals.proposals')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('accounting.proposals.view_proposal')</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons flex-wrap">
                    <a data-toggle="tooltip" title="" href="javascript:void(0);" class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="copyURL" data-original-title="@lang('accounting.proposals.copy')">
                        <i class="iconsax icon text-primary mr-0" icon-name="document-copy"></i>
                    </a>

                    <div class="dropdown">
                        <button class="btn btn-default btn-primary no-wrap wh-45 px-0 dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="iconsax mr-0 fs-18" icon-name="download-1"></i>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" href="#"><i class="las la-file-pdf"></i> @lang('accounting.proposals.pdf')</a>
                            <a class="dropdown-item" href="#"><i class="las la-file-excel"></i> @lang('accounting.proposals.csv')</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Proposal Timeline -->
        @livewire('accounting.proposal.proposal-timeline', ['proposalId' => $proposalId])

        <!-- Action Buttons -->
        <div class="mb-3">
            <div class="d-flex justify-content-end mb-3">
                <div class="btn-group" role="group" aria-label="Basic example">
                    <button type="button" class="btn btn-sm btn-warning">@lang('accounting.proposals.send_reminder')</button>
                    <button type="button" class="btn btn-sm btn-warning">@lang('accounting.proposals.duplicate')</button>
                    <button type="button" class="btn btn-sm btn-warning">@lang('accounting.proposals.export')</button>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="d-flex justify-content-sm-between mb-3">
                <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link rounded {{ $activeTab === 'proposal' ? 'active' : '' }}"
                                wire:click="setActiveTab('proposal')"
                                type="button" role="tab">
                            @lang('accounting.proposals.proposal')
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link rounded {{ $activeTab === 'summary' ? 'active' : '' }}"
                                wire:click="setActiveTab('summary')"
                                type="button" role="tab">
                            @lang('accounting.proposals.proposal_summary')
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link rounded {{ $activeTab === 'attachments' ? 'active' : '' }}"
                                wire:click="setActiveTab('attachments')"
                                type="button" role="tab">
                            @lang('accounting.proposals.attachments')
                        </button>
                    </li>
                </ul>
                <div class="d-flex gap-10"></div>
            </div>

            <!-- Tab Content -->
            <div class="tab-content" id="myTabContent">
                @if($activeTab === 'proposal')
                    <div class="tab-pane fade active show" id="proposal-tab" role="tabpanel">
                        <div id="proposal" dir="{{ App::getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                            @livewire('accounting.proposal.proposal-header', ['proposalId' => $proposalId])
                            <div class="card-body px-0 mx-4 address-box radius-xl pb-0 mb-5 overflow-hidden">
                                <h6 class="text-dark ml-4 mb-4">@lang('accounting.proposals.item_list')</h6>
                                @livewire('accounting.proposal.proposal-line-items', ['proposalId' => $proposalId])
                            </div>
                        </div>
                    </div>
                @elseif($activeTab === 'summary')
                    @livewire('accounting.proposal.proposal-summary-table', ['proposalId' => $proposalId])
                @elseif($activeTab === 'attachments')
                    @livewire('accounting.proposal.proposal-attachment-manager', ['proposalId' => $proposalId])
                @endif
            </div>
        </div>
    </div>
   </div>
</div>
